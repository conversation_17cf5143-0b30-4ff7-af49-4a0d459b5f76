# Electron 安全配置指南

## Content Security Policy (CSP) 配置

### 问题描述

Electron 应用如果没有正确配置 CSP，会在控制台显示安全警告：

```
Electron Security Warning (Insecure Content-Security-Policy) 
This renderer process has either no Content Security Policy set or a policy with "unsafe-eval" enabled. 
This exposes users of this app to unnecessary security risks.
```

### 解决方案

使用 `@mattverse/electron-core` 提供的 CSP 工具：

#### 1. 基本使用

```typescript
import { generateCSP, generateCSPWithGrpc, CSPPresets } from '@mattverse/electron-core'

// 使用预设配置
const csp = CSPPresets.default()

// 使用 Mattverse 预设（包含 gRPC 支持）
const mattverseCsp = CSPPresets.mattverse()

// 或自定义配置
const customCSP = generateCSP({
  allowInlineScripts: true,
  allowInlineStyles: true,
  allowEval: false, // 重要：设为 false 避免安全警告
  connectSrc: ['https://api.example.com'],
})

// 专门为 gRPC 应用生成 CSP
const grpcCSP = generateCSPWithGrpc('***********', 29998, false, {
  allowInlineScripts: true,
  allowInlineStyles: true,
})
```

#### 2. 在 HTML 中使用

```html
<!doctype html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>My Electron App</title>
  <!-- 使用安全的 CSP 配置 -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline';
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
    img-src 'self' data: https: http:;
    font-src 'self' data: https://fonts.gstatic.com;
    connect-src 'self' https: http: ws: wss:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
  " />
  <!-- 注意：frame-ancestors 在 meta 标签中无效，已移除 -->
</head>
<body>
  <!-- 应用内容 -->
</body>
</html>
```

#### 3. 预设配置

```typescript
// 严格配置（生产环境推荐）
const strictCSP = CSPPresets.strict()

// 开发环境配置
const devCSP = CSPPresets.development()

// Vue 应用优化配置
const vueCSP = CSPPresets.vue()

// Mattverse 应用配置（包含 gRPC 支持）
const mattverseCSP = CSPPresets.mattverse()

// 默认配置（平衡安全性和兼容性）
const defaultCSP = CSPPresets.default()
```

#### 4. 配置验证

```typescript
import { validateCSP } from '@mattverse/electron-core'

const csp = "default-src 'self'; script-src 'self' 'unsafe-eval';"
const validation = validateCSP(csp)

if (!validation.isSecure) {
  console.warn('CSP 安全警告:', validation.warnings)
  console.log('建议:', validation.suggestions)
}
```

### gRPC 支持配置

#### 自动配置 gRPC 连接源

```typescript
import { generateCSPWithGrpc } from '@mattverse/electron-core'

// 为特定的 gRPC 服务器生成 CSP
const csp = generateCSPWithGrpc(
  '***********',  // gRPC 主机
  29998,          // gRPC 端口
  false,          // 是否使用安全连接
  {
    // 其他 CSP 选项
    allowInlineScripts: true,
    allowInlineStyles: true,
    connectSrc: ['https://api.example.com'], // 额外的连接源
  }
)
```

#### 手动配置 gRPC

```typescript
import { generateCSP } from '@mattverse/electron-core'

const csp = generateCSP({
  grpcConfig: {
    host: '***********',
    port: 29998,
    secure: false,
    additionalServers: [
      'backup.grpc-server.com:29998',
      'https://secure-grpc.example.com:443',
    ],
  },
  connectSrc: [
    'https://api.example.com',
    'wss://websocket.example.com',
  ],
})
```

#### 环境变量支持

```typescript
const csp = generateCSPWithGrpc(
  process.env.VITE_APP_LINKER_HOST || '***********',
  parseInt(process.env.VITE_APP_LINKER_PORT || '29998'),
  process.env.VITE_APP_LINKER_SECURE === 'true'
)
```

### 配置说明

#### 关键指令解释

- `default-src 'self'`: 默认只允许同源资源
- `script-src 'self' 'unsafe-inline'`: 允许同源和内联脚本
- `style-src 'self' 'unsafe-inline'`: 允许同源和内联样式
- `object-src 'none'`: 禁止所有插件
- `base-uri 'self'`: 限制 base 标签的 URI
- `form-action 'self'`: 限制表单提交目标
- `frame-ancestors 'none'`: 防止被嵌入到 iframe

#### 为什么移除 `unsafe-eval`

- `unsafe-eval` 允许执行动态代码（如 `eval()`）
- 在 Electron 中会触发安全警告
- 现代前端框架（Vue、React）通常不需要 `eval`
- 移除后可以消除控制台警告，提高安全性

### 最佳实践

1. **生产环境使用严格配置**：
   ```typescript
   const csp = CSPPresets.strict()
   ```

2. **开发环境可以放宽限制**：
   ```typescript
   const csp = process.env.NODE_ENV === 'development' 
     ? CSPPresets.development() 
     : CSPPresets.strict()
   ```

3. **定期验证配置**：
   ```typescript
   const validation = validateCSP(csp)
   if (!validation.isSecure) {
     console.warn('需要优化 CSP 配置')
   }
   ```

4. **根据需要添加特定源**：
   ```typescript
   const csp = generateCSP({
     connectSrc: [
       'https://api.your-service.com',
       'wss://websocket.your-service.com',
     ],
     imgSrc: ['https://cdn.your-service.com'],
   })
   ```

### 常见问题

**Q: 控制台出现 "frame-ancestors is ignored when delivered via a meta element" 警告？**

A: 这是正常的。`frame-ancestors` 指令只在 HTTP 头中有效，在 `<meta>` 标签中会被忽略。对于 Electron 应用，这个指令不是必需的，因为应用不会被嵌入到 iframe 中。如果需要在 HTTP 头中设置，可以使用：
```typescript
import { generateCSPForHeader } from '@mattverse/electron-core'
const headerCSP = generateCSPForHeader() // 包含 frame-ancestors
```

**Q: 移除 `unsafe-eval` 后应用无法正常工作？**

A: 检查是否有代码使用了 `eval()`、`Function()` 构造函数或动态代码执行。现代构建工具通常可以避免这些用法。

**Q: 开发环境需要 HMR 支持怎么办？**

A: 在 `connect-src` 中添加 Vite 或 Webpack 的 HMR 端点：
```typescript
connectSrc: ['ws://localhost:5173', 'ws://localhost:3000']
```

**Q: 第三方 CDN 资源加载失败？**

A: 在相应的指令中添加 CDN 域名：
```typescript
styleSrc: ['https://cdn.jsdelivr.net'],
fontSrc: ['https://fonts.gstatic.com'],
```
