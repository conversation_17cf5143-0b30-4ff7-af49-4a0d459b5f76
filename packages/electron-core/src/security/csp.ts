/**
 * Content Security Policy (CSP) 配置工具
 * 为 Electron 应用提供安全的 CSP 配置
 */

export interface CSPOptions {
  /** 是否允许内联脚本 (开发环境可能需要) */
  allowInlineScripts?: boolean
  /** 是否允许内联样式 */
  allowInlineStyles?: boolean
  /** 是否允许 eval (不推荐，会产生安全警告) */
  allowEval?: boolean
  /** 额外的脚本源 */
  scriptSrc?: string[]
  /** 额外的样式源 */
  styleSrc?: string[]
  /** 额外的图片源 */
  imgSrc?: string[]
  /** 额外的字体源 */
  fontSrc?: string[]
  /** 额外的连接源 */
  connectSrc?: string[]
  /** 是否为开发环境 */
  isDevelopment?: boolean
}

/**
 * 生成安全的 CSP 配置字符串
 */
export function generateCSP(options: CSPOptions = {}): string {
  const {
    allowInlineScripts = true,
    allowInlineStyles = true,
    allowEval = false, // 默认不允许 eval，避免安全警告
    scriptSrc = [],
    styleSrc = ['https://fonts.googleapis.com'],
    imgSrc = ['data:', 'https:', 'http:'],
    fontSrc = ['data:', 'https://fonts.gstatic.com'],
    connectSrc = [
      'https:',
      'http:',
      'ws:',
      'wss:',
      'https://api.iconify.design',
      'https://api.simplesvg.com',
      'https://api.unisvg.com',
    ],
    isDevelopment = process.env.NODE_ENV === 'development',
  } = options

  // 基础脚本源
  const baseScriptSrc = ["'self'"]
  if (allowInlineScripts) {
    baseScriptSrc.push("'unsafe-inline'")
  }
  if (allowEval) {
    baseScriptSrc.push("'unsafe-eval'")
    console.warn('⚠️  CSP Warning: unsafe-eval is enabled, this may cause security warnings in Electron')
  }

  // 基础样式源
  const baseStyleSrc = ["'self'"]
  if (allowInlineStyles) {
    baseStyleSrc.push("'unsafe-inline'")
  }

  // 构建 CSP 指令
  const directives = [
    `default-src 'self'`,
    `script-src ${[...baseScriptSrc, ...scriptSrc].join(' ')}`,
    `style-src ${[...baseStyleSrc, ...styleSrc].join(' ')}`,
    `img-src 'self' ${imgSrc.join(' ')}`,
    `font-src 'self' ${fontSrc.join(' ')}`,
    `connect-src 'self' ${connectSrc.join(' ')}`,
    `object-src 'none'`,
    `base-uri 'self'`,
    `form-action 'self'`,
    `frame-ancestors 'none'`,
  ]

  // 开发环境可能需要更宽松的配置
  if (isDevelopment) {
    // 开发环境可以添加一些额外的源
    console.log('🔧 Development mode: Using relaxed CSP configuration')
  }

  return directives.join('; ') + ';'
}

/**
 * 预设的 CSP 配置
 */
export const CSPPresets = {
  /** 严格的生产环境配置 */
  strict: (): string => generateCSP({
    allowInlineScripts: false,
    allowInlineStyles: false,
    allowEval: false,
  }),

  /** 开发环境配置 */
  development: (): string => generateCSP({
    allowInlineScripts: true,
    allowInlineStyles: true,
    allowEval: false, // 即使在开发环境也不推荐 eval
    isDevelopment: true,
  }),

  /** 默认配置 (推荐) */
  default: (): string => generateCSP({
    allowInlineScripts: true,
    allowInlineStyles: true,
    allowEval: false,
  }),

  /** Vue 应用优化配置 */
  vue: (): string => generateCSP({
    allowInlineScripts: true,
    allowInlineStyles: true,
    allowEval: false,
    styleSrc: [
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net', // 可能用到的 CDN
    ],
    connectSrc: [
      'https:',
      'http:',
      'ws:',
      'wss:',
      'https://api.iconify.design',
      'https://api.simplesvg.com',
      'https://api.unisvg.com',
      'https://vitejs.dev', // Vite HMR
    ],
  }),
}

/**
 * 生成 HTML meta 标签
 */
export function generateCSPMetaTag(csp: string): string {
  return `<meta http-equiv="Content-Security-Policy" content="${csp}" />`
}

/**
 * 验证 CSP 配置是否安全
 */
export function validateCSP(csp: string): {
  isSecure: boolean
  warnings: string[]
  suggestions: string[]
} {
  const warnings: string[] = []
  const suggestions: string[] = []

  if (csp.includes("'unsafe-eval'")) {
    warnings.push("使用了 'unsafe-eval'，这会在 Electron 中产生安全警告")
    suggestions.push("移除 'unsafe-eval' 以提高安全性")
  }

  if (csp.includes("'unsafe-inline'") && csp.includes('script-src')) {
    warnings.push("脚本中使用了 'unsafe-inline'，存在 XSS 风险")
    suggestions.push("考虑使用 nonce 或 hash 替代 'unsafe-inline'")
  }

  if (!csp.includes('object-src')) {
    warnings.push("缺少 object-src 指令")
    suggestions.push("添加 'object-src none' 以防止插件执行")
  }

  return {
    isSecure: warnings.length === 0,
    warnings,
    suggestions,
  }
}
