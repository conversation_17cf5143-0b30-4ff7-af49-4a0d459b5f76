/**
 * Content Security Policy (CSP) 配置工具
 * 为 Electron 应用提供安全的 CSP 配置
 */

export interface CSPOptions {
  /** 是否允许内联脚本 (开发环境可能需要) */
  allowInlineScripts?: boolean
  /** 是否允许内联样式 */
  allowInlineStyles?: boolean
  /** 是否允许 eval (不推荐，会产生安全警告) */
  allowEval?: boolean
  /** 额外的脚本源 */
  scriptSrc?: string[]
  /** 额外的样式源 */
  styleSrc?: string[]
  /** 额外的图片源 */
  imgSrc?: string[]
  /** 额外的字体源 */
  fontSrc?: string[]
  /** 额外的连接源 */
  connectSrc?: string[]
  /** gRPC 服务器配置 */
  grpcConfig?: {
    /** gRPC 服务器主机 */
    host?: string
    /** gRPC 服务器端口 */
    port?: number
    /** 是否使用安全连接 */
    secure?: boolean
    /** 额外的 gRPC 服务器地址 */
    additionalServers?: string[]
  }
  /** 是否为开发环境 */
  isDevelopment?: boolean
}

/**
 * 生成安全的 CSP 配置字符串
 */
export function generateCSP(options: CSPOptions = {}): string {
  const {
    allowInlineScripts = true,
    allowInlineStyles = true,
    allowEval = false, // 默认不允许 eval，避免安全警告
    scriptSrc = [],
    styleSrc = ['https://fonts.googleapis.com'],
    imgSrc = ['data:', 'https:', 'http:'],
    fontSrc = ['data:', 'https://fonts.gstatic.com'],
    connectSrc = [
      'https:',
      'http:',
      'ws:',
      'wss:',
      'https://api.iconify.design',
      'https://api.simplesvg.com',
      'https://api.unisvg.com',
    ],
    grpcConfig,
    isDevelopment = process.env.NODE_ENV === 'development',
  } = options

  // 处理 gRPC 连接源
  const grpcConnectSrc: string[] = []
  if (grpcConfig) {
    const { host, port, secure = false, additionalServers = [] } = grpcConfig

    // 添加主 gRPC 服务器
    if (host && port) {
      const protocol = secure ? 'https:' : 'http:'
      grpcConnectSrc.push(`${protocol}//${host}:${port}`)
      grpcConnectSrc.push(`${host}:${port}`) // gRPC 可能需要不带协议的格式
    }

    // 添加额外的 gRPC 服务器
    grpcConnectSrc.push(...additionalServers)
  }

  // 基础脚本源
  const baseScriptSrc = ["'self'"]
  if (allowInlineScripts) {
    baseScriptSrc.push("'unsafe-inline'")
  }
  if (allowEval) {
    baseScriptSrc.push("'unsafe-eval'")
    // 注意：unsafe-eval 会在 Electron 中产生安全警告
  }

  // 基础样式源
  const baseStyleSrc = ["'self'"]
  if (allowInlineStyles) {
    baseStyleSrc.push("'unsafe-inline'")
  }

  // 合并所有连接源（包括 gRPC）
  const allConnectSrc = [...connectSrc, ...grpcConnectSrc]

  // 构建 CSP 指令
  const directives = [
    `default-src 'self'`,
    `script-src ${[...baseScriptSrc, ...scriptSrc].join(' ')}`,
    `style-src ${[...baseStyleSrc, ...styleSrc].join(' ')}`,
    `img-src 'self' ${imgSrc.join(' ')}`,
    `font-src 'self' ${fontSrc.join(' ')}`,
    `connect-src 'self' ${allConnectSrc.join(' ')}`,
    `object-src 'none'`,
    `base-uri 'self'`,
    `form-action 'self'`,
    // 注意：frame-ancestors 在 meta 标签中会被忽略，只在 HTTP 头中有效
  ]

  // 开发环境可能需要更宽松的配置
  if (isDevelopment) {
    // 开发环境可以添加一些额外的源，如 HMR 端点
  }

  return directives.join('; ') + ';'
}

/**
 * 预设的 CSP 配置
 */
export const CSPPresets = {
  /** 严格的生产环境配置 */
  strict: (): string =>
    generateCSP({
      allowInlineScripts: false,
      allowInlineStyles: false,
      allowEval: false,
    }),

  /** 开发环境配置 */
  development: (): string =>
    generateCSP({
      allowInlineScripts: true,
      allowInlineStyles: true,
      allowEval: false, // 即使在开发环境也不推荐 eval
      isDevelopment: true,
    }),

  /** 默认配置 (推荐) */
  default: (): string =>
    generateCSP({
      allowInlineScripts: true,
      allowInlineStyles: true,
      allowEval: false,
    }),

  /** Vue 应用优化配置 */
  vue: (): string =>
    generateCSP({
      allowInlineScripts: true,
      allowInlineStyles: true,
      allowEval: false,
      styleSrc: [
        'https://fonts.googleapis.com',
        'https://cdn.jsdelivr.net', // 可能用到的 CDN
      ],
      connectSrc: [
        'https:',
        'http:',
        'ws:',
        'wss:',
        'https://api.iconify.design',
        'https://api.simplesvg.com',
        'https://api.unisvg.com',
        'https://vitejs.dev', // Vite HMR
      ],
    }),

  /** Mattverse 应用配置（包含 gRPC 支持） */
  mattverse: (): string =>
    generateCSP({
      allowInlineScripts: true,
      allowInlineStyles: true,
      allowEval: false,
      styleSrc: ['https://fonts.googleapis.com', 'https://cdn.jsdelivr.net'],
      connectSrc: [
        'https:',
        'http:',
        'ws:',
        'wss:',
        'https://api.iconify.design',
        'https://api.simplesvg.com',
        'https://api.unisvg.com',
        'https://vitejs.dev', // Vite HMR
      ],
      grpcConfig: {
        host: '***********',
        port: 29998,
        secure: false,
      },
    }),
}

/**
 * 生成包含 gRPC 支持的 CSP 配置
 */
export function generateCSPWithGrpc(
  grpcHost: string,
  grpcPort: number,
  secure = false,
  additionalOptions: Omit<CSPOptions, 'grpcConfig'> = {}
): string {
  return generateCSP({
    ...additionalOptions,
    grpcConfig: {
      host: grpcHost,
      port: grpcPort,
      secure,
    },
  })
}

/**
 * 生成用于 HTTP 头的 CSP（包含所有指令）
 */
export function generateCSPForHeader(options: CSPOptions = {}): string {
  const baseCsp = generateCSP(options)
  // HTTP 头中可以包含 frame-ancestors
  return baseCsp.replace(';', "; frame-ancestors 'none';")
}

/**
 * 生成 HTML meta 标签
 */
export function generateCSPMetaTag(csp: string): string {
  return `<meta http-equiv="Content-Security-Policy" content="${csp}" />`
}

/**
 * 验证 CSP 配置是否安全
 */
export function validateCSP(csp: string): {
  isSecure: boolean
  warnings: string[]
  suggestions: string[]
} {
  const warnings: string[] = []
  const suggestions: string[] = []

  if (csp.includes("'unsafe-eval'")) {
    warnings.push("使用了 'unsafe-eval'，这会在 Electron 中产生安全警告")
    suggestions.push("移除 'unsafe-eval' 以提高安全性")
  }

  if (csp.includes("'unsafe-inline'") && csp.includes('script-src')) {
    warnings.push("脚本中使用了 'unsafe-inline'，存在 XSS 风险")
    suggestions.push("考虑使用 nonce 或 hash 替代 'unsafe-inline'")
  }

  if (!csp.includes('object-src')) {
    warnings.push('缺少 object-src 指令')
    suggestions.push("添加 'object-src none' 以防止插件执行")
  }

  return {
    isSecure: warnings.length === 0,
    warnings,
    suggestions,
  }
}
