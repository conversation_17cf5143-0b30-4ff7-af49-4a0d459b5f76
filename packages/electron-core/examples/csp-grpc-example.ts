/**
 * CSP with gRPC Support Example
 * 展示如何为 Electron 应用配置包含 gRPC 支持的 CSP
 */

import {
  generateCSP,
  generateCSPWithGrpc,
  generateCSPMetaTag,
  CSPPresets,
  validateCSP,
} from '@mattverse/electron-core'

// 示例 1: 使用预设配置
function example1_presets() {
  console.log('=== 示例 1: 使用预设配置 ===')
  
  // Mattverse 应用预设（包含默认 gRPC 配置）
  const mattverseCsp = CSPPresets.mattverse()
  console.log('Mattverse CSP:', mattverseCsp)
  
  // 生成 HTML meta 标签
  const metaTag = generateCSPMetaTag(mattverseCsp)
  console.log('Meta Tag:', metaTag)
}

// 示例 2: 自定义 gRPC 配置
function example2_customGrpc() {
  console.log('\n=== 示例 2: 自定义 gRPC 配置 ===')
  
  // 使用便捷函数生成 gRPC CSP
  const grpcCsp = generateCSPWithGrpc(
    '*************', // 自定义 gRPC 主机
    8080,            // 自定义端口
    true,            // 使用安全连接
    {
      allowInlineScripts: true,
      allowInlineStyles: true,
      connectSrc: [
        'https://api.custom.com',
        'wss://websocket.custom.com',
      ],
    }
  )
  
  console.log('Custom gRPC CSP:', grpcCsp)
}

// 示例 3: 完全自定义配置
function example3_fullCustom() {
  console.log('\n=== 示例 3: 完全自定义配置 ===')
  
  const customCsp = generateCSP({
    allowInlineScripts: true,
    allowInlineStyles: true,
    allowEval: false,
    grpcConfig: {
      host: process.env.GRPC_HOST || '***********',
      port: parseInt(process.env.GRPC_PORT || '29998'),
      secure: process.env.GRPC_SECURE === 'true',
      additionalServers: [
        'backup-grpc.example.com:29998',
        'https://secure-grpc.example.com:443',
      ],
    },
    connectSrc: [
      'https://api.iconify.design',
      'https://fonts.googleapis.com',
      'wss://realtime.example.com',
    ],
    styleSrc: [
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net',
    ],
    imgSrc: [
      'data:',
      'https:',
      'https://images.example.com',
    ],
  })
  
  console.log('Full Custom CSP:', customCsp)
}

// 示例 4: 开发环境 vs 生产环境
function example4_environments() {
  console.log('\n=== 示例 4: 环境配置 ===')
  
  const isDevelopment = process.env.NODE_ENV === 'development'
  
  if (isDevelopment) {
    // 开发环境：包含 HMR 支持
    const devCsp = generateCSPWithGrpc(
      'localhost',
      29998,
      false,
      {
        allowInlineScripts: true,
        allowInlineStyles: true,
        connectSrc: [
          'ws://localhost:5173',  // Vite HMR
          'ws://localhost:3000',  // 其他开发服务器
          'https://vitejs.dev',
        ],
        isDevelopment: true,
      }
    )
    console.log('Development CSP:', devCsp)
  } else {
    // 生产环境：更严格的配置
    const prodCsp = generateCSPWithGrpc(
      '***********',
      29998,
      true, // 生产环境使用安全连接
      {
        allowInlineScripts: false, // 生产环境禁用内联脚本
        allowInlineStyles: false,  // 生产环境禁用内联样式
        connectSrc: [
          'https://api.production.com',
        ],
      }
    )
    console.log('Production CSP:', prodCsp)
  }
}

// 示例 5: CSP 验证
function example5_validation() {
  console.log('\n=== 示例 5: CSP 验证 ===')
  
  const csp = generateCSPWithGrpc('***********', 29998)
  const validation = validateCSP(csp)
  
  console.log('CSP 安全性:', validation.isSecure ? '✅ 安全' : '⚠️ 有风险')
  
  if (validation.warnings.length > 0) {
    console.log('警告:')
    validation.warnings.forEach(warning => console.log(`  - ${warning}`))
  }
  
  if (validation.suggestions.length > 0) {
    console.log('建议:')
    validation.suggestions.forEach(suggestion => console.log(`  - ${suggestion}`))
  }
}

// 示例 6: 在 Electron 主进程中使用
function example6_electronMain() {
  console.log('\n=== 示例 6: Electron 主进程集成 ===')
  
  // 模拟从环境变量或配置文件读取 gRPC 配置
  const grpcConfig = {
    host: process.env.VITE_APP_LINKER_HOST || '***********',
    port: parseInt(process.env.VITE_APP_LINKER_PORT || '29998'),
    secure: process.env.VITE_APP_LINKER_SECURE === 'true',
  }
  
  const csp = generateCSP({
    grpcConfig,
    allowInlineScripts: true,
    allowInlineStyles: true,
    connectSrc: [
      'https://api.iconify.design',
      'https://api.simplesvg.com',
      'https://api.unisvg.com',
    ],
  })
  
  console.log('Electron Main Process CSP:', csp)
  
  // 可以将此 CSP 写入到 HTML 模板或通过 IPC 传递给渲染进程
  const htmlTemplate = `
<!doctype html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Electron App with gRPC</title>
  ${generateCSPMetaTag(csp)}
</head>
<body>
  <div id="app"></div>
</body>
</html>`
  
  console.log('HTML Template with CSP:', htmlTemplate)
}

// 运行所有示例
function runAllExamples() {
  example1_presets()
  example2_customGrpc()
  example3_fullCustom()
  example4_environments()
  example5_validation()
  example6_electronMain()
}

// 如果直接运行此文件
if (require.main === module) {
  runAllExamples()
}

export {
  example1_presets,
  example2_customGrpc,
  example3_fullCustom,
  example4_environments,
  example5_validation,
  example6_electronMain,
  runAllExamples,
}
