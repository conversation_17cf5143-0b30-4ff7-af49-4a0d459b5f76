/* UI Components 样式 - shadcn-vue + Tailwind CSS v4 */
@import 'tailwindcss';
@import '@mattverse/shared/styles/theme.css';

@custom-variant dark (&:is(.dark *));

/* ===== shadcn-vue 基础样式 ===== */
@layer base {
  * {
    /* @apply border-border; */
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

/* ===== 组件样式增强 ===== */
@layer components {
  /* 确保侧边栏组件样式正确应用 */
  .sidebar {
    background-color: hsl(var(--sidebar));
    color: hsl(var(--sidebar-foreground));
    /* border-color: hsl(var(--sidebar-border)); */
  }

  .sidebar-primary {
    background-color: hsl(var(--sidebar-primary));
    color: hsl(var(--sidebar-primary-foreground));
  }

  .sidebar-accent {
    background-color: hsl(var(--sidebar-accent));
    color: hsl(var(--sidebar-accent-foreground));
  }
}
