import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue(), tailwindcss()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'MattverseUIComponents',
      fileName: (format) => `index.${format}.js`,
      formats: ['es', 'cjs'],
    },
    rollupOptions: {
      external: [
        'vue',
        'reka-ui',
        'class-variance-authority',
        'clsx',
        'tailwind-merge',
        'lucide-vue-next',
        'radix-vue',
        'vue-sonner',
        'tailwindcss-animate',
      ],
      output: {
        globals: {
          vue: 'Vue',
          'reka-ui': 'RekaUI',
          'class-variance-authority': 'ClassVarianceAuthority',
          clsx: 'clsx',
          'tailwind-merge': 'tailwindMerge',
          'lucide-vue-next': 'LucideVueNext',
          'radix-vue': 'RadixVue',
          'vue-sonner': 'VueSonner',
        },
      },
    },
    cssCodeSplit: false,
    sourcemap: true,
  },
})
