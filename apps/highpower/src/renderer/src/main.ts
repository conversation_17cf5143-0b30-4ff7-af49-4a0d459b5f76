import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import App from './App.vue'
import router from './router'

// 1. 首先导入应用样式（包含 Tailwind CSS）
import './assets/styles/index.css'
// 2. 然后导入组件库样式
import '@mattverse/mattverse-ui/style.css'
const app = createApp(App)

// Setup Pinia store
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)

// Setup router
app.use(router)

app.mount('#app')
