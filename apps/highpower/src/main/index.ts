/**
 * HighPower 主进程
 */
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import { createElectronApp, logger,createLinkerClient,type LinkerGrpcClient } from '@mattverse/electron-core'


const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 全局 gRPC 客户端
let grpcClient: LinkerGrpcClient | null = null

// 初始化 gRPC 客户端
function initGrpcClient() {
  try {
    const config = {
      host: process.env.VITE_APP_LINKER_HOST || '***********',
      port: parseInt(process.env.VITE_APP_LINKER_PORT || '29998'),
      secure: false
    }

    const userId = process.env.VITE_APP_USER_ID || '0'
    const token = process.env.VITE_APP_USER_TOKEN || 'abcdefghijklmn'

    grpcClient = createLinkerClient(config, userId, token)

    logger.info('gRPC 客户端初始化成功:', {
      host: config.host,
      port: config.port,
      userId,
      token: token
    })

    return grpcClient
  } catch (error) {
    logger.error('gRPC 客户端初始化失败:', error)
    return null
  }
}

// 创建 HighPower 应用
const app = createElectronApp({
  window: {
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 700,
    show: true, // 确保立即显示窗口
    // backgroundColor: '#0f172a', // 设置深色背景，与应用主题一致
    webPreferences: {
      preload: join(__dirname, '../preload/index.js')
    }
  },
  handlers: {
    // HighPower 特定的 IPC 处理器
    'highpower:get-config': async () => {
      return {
        appName: 'HighPower',
        version: '1.1.0',
        features: ['high-performance', 'computing', 'workflow', 'gpu-acceleration']
      }
    },


    'compute:get-status': async (jobId: string) => {
      logger.info('Getting compute job status:', jobId)
      // 这里实现具体的任务状态查询逻辑
      return {
        jobId,
        status: 'running',
        progress: 0.5,
        estimatedTime: 120
      }
    },

    // HighPower 特定功能处理器
    'highpower:get-gpu-info': async () => {
      logger.info('Getting GPU information')
      // 这里实现获取GPU信息的逻辑
      return {
        gpuCount: 1,
        gpus: [
          {
            name: 'NVIDIA GeForce RTX 4080',
            memory: '16GB',
            utilization: 25,
            temperature: 65
          }
        ]
      }
    },

    'highpower:get-system-resources': async () => {
      logger.info('Getting system resources')
      return {
        cpu: {
          usage: 45,
          cores: 16,
          threads: 32
        },
        memory: {
          total: '32GB',
          used: '12GB',
          available: '20GB'
        },
        disk: {
          total: '1TB',
          used: '500GB',
          available: '500GB'
        }
      }
    },

    'highpower:optimize-performance': async (settings: unknown) => {
      logger.info('Optimizing performance with settings:', settings)
      return {
        success: true,
        message: '性能优化设置已应用',
        settings
      }
    },

    // gRPC 相关处理器
    'grpc:init': async () => {
      try {
        if (!grpcClient) {
          grpcClient = initGrpcClient()
        }
        return {
          success: !!grpcClient,
          connected: grpcClient?.isConnected() || false,
          message: grpcClient ? 'gRPC 客户端已初始化' : 'gRPC 客户端初始化失败'
        }
      } catch (error) {
        logger.error('gRPC 初始化失败:', error)
        return {
          success: false,
          connected: false,
          error: error instanceof Error ? error.message : '初始化失败'
        }
      }
    },

    'grpc:ping': async () => {
      return new Promise((resolve) => {
        if (!grpcClient) {
          resolve({
            success: false,
            error: 'gRPC 客户端未初始化'
          })
          return
        }

        grpcClient.ping((error, response) => {
          if (error) {
            logger.error('gRPC Ping 失败:', error)
            resolve({
              success: false,
              error: error.message
            })
          } else {
            logger.info('gRPC Ping 成功:', response)
            resolve({
              success: true,
              data: response
            })
          }
        })
      })
    },

    'grpc:call': async (apiName: string, params: Record<string, any>) => {
      return new Promise((resolve) => {
        if (!grpcClient) {
          resolve({
            success: false,
            error: 'gRPC 客户端未初始化'
          })
          return
        }

        grpcClient.call(apiName, params, (error, response) => {
          if (error) {
            logger.error(`gRPC 调用 ${apiName} 失败:`, error)
            resolve({
              success: false,
              error: error.message
            })
          } else {
            logger.info(`gRPC 调用 ${apiName} 成功:`, response)
            resolve({
              success: true,
              data: response
            })
          }
        })
      })
    },

    'grpc:get-status': async () => {
      return {
        initialized: !!grpcClient,
        connected: grpcClient?.isConnected() || false,
        url: grpcClient?.getLinkerUrl() || null,
        userId: grpcClient?.getUserId() || null
      }
    }
  },
  onReady: () => {
    logger.info('HighPower app is ready!')

    // 初始化 gRPC 客户端
    try {
      grpcClient = initGrpcClient()
      if (grpcClient) {
        logger.info('gRPC 客户端已在应用启动时初始化')
      }
    } catch (error) {
      logger.error('应用启动时 gRPC 客户端初始化失败:', error)
    }
  },
  onWindowCreated: (window) => {
    // 开发环境下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      window.webContents.openDevTools()
    }
    logger.info('HighPower window created!')
  }
})

// 启动应用
app.start().catch((error) => {
  logger.error('Failed to start HighPower app:', error)
  process.exit(1)
})
