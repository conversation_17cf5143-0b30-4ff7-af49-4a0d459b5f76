<!doctype html>
<html>

<head>
  <meta charset="UTF-8" />
  <title id="app-title">MattVerse 电池设计自动化平台</title>
  <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'self' https: http:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: http:; connect-src 'self' https: http: ws: wss:; font-src 'self' data:" />
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: http:; connect-src 'self' https://api.iconify.design https://api.simplesvg.com https://api.unisvg.com ws: wss:; font-src 'self' data:" />
  <style>
    /* 添加跳动动画 */
    @keyframes bounce {

      0%,
      100% {
        transform: translateY(0);
      }

      50% {
        transform: translateY(-15px);
      }
    }

    /* 添加文字渐变动画 */
    @keyframes gradientShift {
      0% {
        background-position: 0% 50%;
      }

      50% {
        background-position: 100% 50%;
      }

      100% {
        background-position: 0% 50%;
      }
    }

    .gradient-text {
      background: linear-gradient(90deg, #3b82f6, #10b981, #3b82f6);
      background-size: 200% auto;
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      animation:
        gradientShift 3s ease infinite,
        bounce 1s ease infinite;
    }

    .dark .gradient-text {
      background: linear-gradient(90deg, #60a5fa, #34d399, #60a5fa);
      background-size: 200% auto;
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    /* 加载动画 */
    @keyframes loading {
      0% { transform: translateX(-100%); }
      50% { transform: translateX(0%); }
      100% { transform: translateX(100%); }
    }
  </style>
  <script>
    console.log('index.html loaded')
      // 初始化主题 - 需要在 Vue 应用启动前执行，避免主题闪烁
      // 注意：这里使用 VueUse 的 localStorage key，与 settings store 保持同步
      ; (function () {
        const theme = localStorage.getItem('vueuse-color-scheme') || 'auto'
        if (
          theme === 'dark' ||
          (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)
        ) {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
      })()
  </script>
</head>

<body>
  <div id="app">
    <div id="loading-screen" style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          font-size: 3rem;
          font-weight: bold;
          z-index: 9999;
          background-color: rgba(255, 255, 255, 0.8);
        " class="dark:bg-opacity-80 dark:bg-gray-900">
      <div style="margin-bottom: 2rem;">
        <span class="gradient-text">MattVerse</span>
      </div>
      <div style="width: 200px; height: 4px; background: rgba(0,0,0,0.1); border-radius: 2px; overflow: hidden;" class="dark:bg-white/20">
        <div style="width: 100%; height: 100%; background: linear-gradient(90deg, #3b82f6, #10b981); animation: loading 2s ease-in-out infinite;"></div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>
