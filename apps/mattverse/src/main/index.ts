/**
 * Mattverse 主进程
 */
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import {
  createElectronApp,
  logger,
  createLinkerClient,
  type LinkerGrpcClient,
} from '@mattverse/electron-core'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 全局 gRPC 客户端
let grpcClient: LinkerGrpcClient | null = null

// 初始化 gRPC 客户端
function initGrpcClient() {
  try {
    const config = {
      host: process.env.VITE_APP_LINKER_HOST || '***********',
      port: parseInt(process.env.VITE_APP_LINKER_PORT || '29998'),
      secure: false,
    }

    const userId = process.env.VITE_APP_USER_ID || '0'
    const token = process.env.VITE_APP_USER_TOKEN || 'abcdefghijklmn'

    grpcClient = createLinkerClient(config, userId, token)

    logger.info('Mattverse gRPC 客户端初始化成功:', {
      host: config.host,
      port: config.port,
      userId,
      token: token,
    })

    return grpcClient
  } catch (error) {
    logger.error('Mattverse gRPC 客户端初始化失败:', error)
    return null
  }
}

// 创建 Mattverse 应用
const app = createElectronApp({
  window: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: true,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
    },
  },
  // 配置开发者工具
  devTools: {
    enabled: true,
    extensions: ['VUE_DEVTOOLS'], // 安装 Vue 开发者工具
    autoOpen: true, // 开发环境自动打开
    forceInProduction: false, // 生产环境不启用
  },
  handlers: {
    // Mattverse 特定的 IPC 处理器
    'mattverse:get-config': async () => {
      return {
        appName: 'Mattverse',
        version: '1.1.0',
        features: ['workflow', 'ai', 'automation'],
      }
    },

    // gRPC 相关处理器
    'grpc:init': async () => {
      try {
        if (!grpcClient) {
          grpcClient = initGrpcClient()
        }
        return {
          success: !!grpcClient,
          connected: grpcClient?.isConnected() || false,
          message: grpcClient ? 'gRPC 客户端已初始化' : 'gRPC 客户端初始化失败',
        }
      } catch (error) {
        logger.error('gRPC 初始化失败:', error)
        return {
          success: false,
          connected: false,
          error: error instanceof Error ? error.message : '初始化失败',
        }
      }
    },

    'grpc:ping': async () => {
      return new Promise((resolve) => {
        if (!grpcClient) {
          resolve({
            success: false,
            error: 'gRPC 客户端未初始化',
          })
          return
        }

        grpcClient.ping((error, response) => {
          if (error) {
            logger.error('gRPC Ping 失败:', error)
            resolve({
              success: false,
              error: error.message,
            })
          } else {
            logger.info('gRPC Ping 成功:', response)
            resolve({
              success: true,
              data: response,
            })
          }
        })
      })
    },

    'grpc:call': async (apiName: string, params: Record<string, any>) => {
      return new Promise((resolve) => {
        if (!grpcClient) {
          resolve({
            success: false,
            error: 'gRPC 客户端未初始化',
          })
          return
        }

        grpcClient.call(apiName, params, (error, response) => {
          if (error) {
            logger.error(`gRPC 调用 ${apiName} 失败:`, error)
            resolve({
              success: false,
              error: error.message,
            })
          } else {
            logger.info(`gRPC 调用 ${apiName} 成功:`, response)
            resolve({
              success: true,
              data: response,
            })
          }
        })
      })
    },

    'grpc:get-status': async () => {
      return {
        initialized: !!grpcClient,
        connected: grpcClient?.isConnected() || false,
        url: grpcClient?.getLinkerUrl() || null,
        userId: grpcClient?.getUserId() || null,
      }
    },
  },
  onReady: () => {
    logger.info('Mattverse app is ready!')

    // 初始化 gRPC 客户端
    try {
      grpcClient = initGrpcClient()
      if (grpcClient) {
        logger.info('Mattverse gRPC 客户端已在应用启动时初始化')
      }
    } catch (error) {
      logger.error('Mattverse 应用启动时 gRPC 客户端初始化失败:', error)
    }
  },
  onWindowCreated: () => {
    logger.info('Mattverse window created!')
  },
})

// 启动应用
app.start().catch((error) => {
  logger.error('Failed to start Mattverse app:', error)
  process.exit(1)
})
