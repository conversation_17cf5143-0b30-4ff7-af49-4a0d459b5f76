import { defineConfig } from 'electron-vite'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { autoImportPresets, createCopyProtosPlugin } from '@mattverse/configs'

export default defineConfig({
  main: {
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/main'),
        '@core': resolve(__dirname, '../../packages/electron-core/src'),
      },
    },
    plugins: [
      createCopyProtosPlugin({
        sourceDir: '../../packages/electron-core/src/grpc/protos',
        targetDir: 'out/main/protos',
        files: 'all',
        verbose: true,
      }),
    ],
    build: {
      rollupOptions: {
        external: ['@grpc/grpc-js', '@grpc/proto-loader'],
      },
    },
  },
  preload: {
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/preload'),
        '@core': resolve(__dirname, '../../packages/electron-core/src'),
      },
    },
    build: {
      rollupOptions: {
        external: ['protobufjs', '@grpc/grpc-js', '@grpc/proto-loader'],
      },
    },
  },
  renderer: {
    plugins: [vue(), tailwindcss(), ...autoImportPresets.default('mattverse')],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/renderer/src'),
        '@ui': resolve(__dirname, '../../packages/mattverse-ui/src'),
        '@flow': resolve(__dirname, '../../packages/mattverse-flow/src'),
        '@configs': resolve(__dirname, '../../packages/configs/src'),
      },
    },
    assetsInclude: ['**/*.ttf', '**/*.woff', '**/*.woff2'],
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        '@vueuse/core',
        'lucide-vue-next',
        'radix-vue'
      ],
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router'],
            'ui-vendor': ['radix-vue', 'lucide-vue-next'],
            'utils-vendor': ['@vueuse/core']
          }
        }
      }
    }
  },
})
